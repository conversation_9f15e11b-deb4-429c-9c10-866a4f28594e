# 备忘录删除功能实现说明

## 功能概述

为memoSidebar备忘录侧边栏添加了删除功能，参考了historySidebar的删除实现。用户可以删除备忘录中的各种信息项目。

## 实现特点

### 1. 智能删除判断
- **事件记忆删除**：对于"事件记忆"和"最近记忆"分类的项目，使用`deletePersonEvent` API删除
- **个人信息属性删除**：对于其他分类的项目，通过`updatePerson` API更新用户信息来删除对应属性

### 2. 用户界面
- 每个备忘录项目右侧显示删除按钮（悬停时显示）
- 点击删除按钮弹出确认对话框
- 删除过程中显示加载状态
- 删除成功后自动刷新数据

### 3. 数据处理
- 获取原始数据用于准确匹配要删除的项目
- 事件删除：通过描述文本匹配找到对应的event_id
- 属性删除：直接从key_attributes中删除对应的键值对

## 文件修改

### src/components/Chat/memoSidebar.vue

#### 模板变更
```vue
<!-- 原来的结构 -->
<div class="memo-item">
  <div class="memo-label">{{ item.label }}</div>
  <div class="memo-value">{{ item.value }}</div>
</div>

<!-- 新的结构 -->
<div class="memo-item">
  <div class="memo-content">
    <div class="memo-label">{{ item.label }}</div>
    <div class="memo-value">{{ item.value }}</div>
  </div>
  <div class="memo-actions">
    <button class="delete-memo-btn" @click.stop="handleDeleteMemo(category, item)">
      <img src="@/assets/icon/delete.png" alt="删除" />
    </button>
  </div>
</div>
```

#### 新增功能
1. **删除状态管理**
   - `showDeleteDialog`: 控制删除确认对话框显示
   - `isDeleting`: 删除过程中的加载状态
   - `deleteItemInfo`: 存储要删除的项目信息
   - `originalUserProfile`: 存储原始数据用于删除操作

2. **删除相关函数**
   - `handleDeleteMemo()`: 处理删除按钮点击
   - `confirmDeleteMemo()`: 确认删除操作
   - `handleDeleteEvent()`: 删除事件记忆
   - `handleDeleteAttribute()`: 删除个人信息属性
   - `fetchOriginalMemoData()`: 获取原始数据

#### 样式更新
- 调整memo-item布局为flex横向排列
- 添加删除按钮样式（悬停显示）
- 删除按钮使用红色主题

## API调用

### 删除事件记忆
```typescript
await deletePersonEvent({
  user_id: userId,
  event_id: matchingEvent.event_id,
});
```

### 删除个人信息属性
```typescript
await updatePerson(person.person_id, {
  user_id: userId,
  canonical_name: person.canonical_name,
  aliases: person.aliases || '',
  relationships: person.relationships || [],
  profile_summary: person.profile_summary || '',
  key_attributes: updatedAttributes, // 删除了对应属性的新对象
  is_user: person.is_user || false,
  avatar: person.avatar || '',
});
```

## 使用方法

1. 打开备忘录侧边栏
2. 悬停在任意备忘录项目上
3. 点击右侧出现的删除按钮
4. 在确认对话框中点击"确认删除"
5. 等待删除完成，数据会自动刷新

## 注意事项

1. **数据匹配**：事件删除依赖描述文本匹配，可能存在匹配不准确的情况
2. **权限控制**：删除操作不可逆，建议添加更严格的权限控制
3. **错误处理**：已添加完整的错误处理和用户提示
4. **数据同步**：删除成功后会重新获取最新数据

## 扩展建议

1. 可以添加批量删除功能
2. 可以添加删除历史记录和恢复功能
3. 可以优化事件匹配算法，提高准确性
4. 可以添加删除前的二次确认机制
