<template>
  <div class="memo-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="memo-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">备忘录</div>
        <div class="intimacy-display">
          <span class="intimacy-label">当前懂量：</span>
          <span class="intimacy-value">{{ intimacyScore }}</span>
        </div>
        <div class="close-btn" @click="handleClose">
          <img src="@/assets/img/close.png" alt="关闭" />
        </div>
      </div>
      <div class="sidebar-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-text">{{ error }}</div>
          <div class="retry-btn" @click="fetchMemoData">重试</div>
        </div>
        <!-- 备忘录内容 -->
        <div v-else class="memo-list">
          <!-- 动态渲染分类 -->
          <div v-for="category in memoCategories" :key="category.title" class="memo-category">
            <div class="category-title">{{ category.title }}</div>
            <div class="memo-items">
              <div v-for="item in category.items" :key="item.label" class="memo-item">
                <div class="memo-content">
                  <div class="memo-label">{{ item.label }}</div>
                  <div class="memo-value">{{ item.value }}</div>
                </div>
                <div class="memo-actions">
                  <button class="delete-memo-btn" title="删除" @click.stop="handleDeleteMemo(category, item)">
                    <img src="@/assets/icon/delete.png" alt="删除" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <!-- 空状态 -->
          <div v-if="memoCategories.length === 0" class="empty-state">
            <div class="empty-text">暂无备忘录信息</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="`确定要删除这个${deleteItemInfo.type}吗？`"
    :hint="deleteItemInfo.content"
    :is-loading="isDeleting"
    @confirm="confirmDeleteMemo"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { watch, ref, onMounted } from 'vue';
import { showSuccessToast, showFailToast } from 'vant';
import { useUserStore } from '@/stores/user';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import { getMemoInfo, type IMemoCategory, type IMemoItem } from '../../apis/memo';
import { getUserInfo } from '../../apis/common';
import { getIntimacy, deletePersonEvent } from '../../apis/memory';
import { updatePerson } from '../../apis/relation';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);

// 响应式数据
const loading = ref(false);
const error = ref('');
const memoCategories = ref<IMemoCategory[]>([]);
const intimacyScore = ref<number | string>('--');

// 用户存储
const userStore = useUserStore();

// 删除相关状态
const showDeleteDialog = ref(false);
const isDeleting = ref(false);
const deleteItemInfo = ref<{
  type: string;
  content: string;
  category: string;
  item: IMemoItem;
  categoryTitle: string;
}>({
  type: '',
  content: '',
  category: '',
  item: { label: '', value: '' },
  categoryTitle: '',
});

// 存储原始数据用于删除操作
const originalUserProfile = ref<any>(null);

// 获取懂量数据
const fetchIntimacyData = async (userId: string) => {
  try {
    console.log('🔄 [memoSidebar] 开始获取懂量数据, userId:', userId);

    const intimacyData = await getIntimacy({ user_id: userId });
    intimacyScore.value = intimacyData.intimacy_score || 0;

    console.log('✅ [memoSidebar] 懂量数据获取成功:', {
      intimacy_score: intimacyScore.value,
      level: intimacyData.level,
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取懂量数据失败:', err);
    intimacyScore.value = '--';
  }
};

// 获取原始备忘录数据用于删除操作
const fetchOriginalMemoData = async (userId: string) => {
  try {
    const response = await fetch(`/humanrelation/get_user_profile_and_events?user_id=${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('获取原始数据失败');
    }

    const data = await response.json();
    originalUserProfile.value = data;
    return data;
  } catch (err) {
    console.error('❌ [memoSidebar] 获取原始数据失败:', err);
    throw err;
  }
};

// 获取备忘录数据
const fetchMemoData = async () => {
  loading.value = true;
  error.value = '';

  try {
    // 获取用户信息以获取 mis_id
    let userId = '';

    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      // 如果 userStore 中没有用户信息，则直接调用接口获取
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (!userId) {
      error.value = '用户信息不存在';
      return;
    }

    console.log('🔄 [memoSidebar] 开始获取备忘录数据, userId:', userId);

    // 并行获取备忘录数据和懂量数据，同时获取原始数据用于删除操作
    const [memoData] = await Promise.all([
      getMemoInfo(userId),
      fetchIntimacyData(userId),
      // 获取原始数据用于删除操作
      fetchOriginalMemoData(userId),
    ]);

    // 调用备忘录接口（数据处理已在API层完成）
    memoCategories.value = memoData;

    console.log('✅ [memoSidebar] 备忘录数据获取成功:', {
      categories_count: memoCategories.value.length,
      categories: memoCategories.value.map((cat) => ({ title: cat.title, items_count: cat.items.length })),
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取备忘录数据失败:', err);
    error.value = '网络错误，请重试';
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      // 侧边栏打开时获取数据
      void fetchMemoData();
    } else {
      document.body.style.overflow = '';
    }
  },
);

// 组件挂载时如果侧边栏已经打开，则获取数据
onMounted(() => {
  if (props.isOpen) {
    void fetchMemoData();
  }
});

// 处理删除备忘录项目
const handleDeleteMemo = (category: IMemoCategory, item: IMemoItem) => {
  console.log('🗑️ [memoSidebar] 触发删除备忘录项目:', { category: category.title, item });

  // 设置删除确认弹窗信息
  deleteItemInfo.value = {
    type: '备忘录项目',
    content: `${item.label}: ${item.value}`,
    category: category.title,
    item,
    categoryTitle: category.title,
  };
  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  deleteItemInfo.value = {
    type: '',
    content: '',
    category: '',
    item: { label: '', value: '' },
    categoryTitle: '',
  };
};

// 确认删除备忘录项目
const confirmDeleteMemo = async () => {
  if (!deleteItemInfo.value.item.label) return;

  try {
    isDeleting.value = true;
    console.log('🔄 [memoSidebar] 开始删除备忘录项目...', deleteItemInfo.value);

    const { categoryTitle, item } = deleteItemInfo.value;

    // 判断删除类型：事件记忆 vs 个人信息属性
    if (categoryTitle === '事件记忆' || categoryTitle === '最近记忆') {
      await handleDeleteEvent(item);
    } else {
      await handleDeleteAttribute(item);
    }

    showSuccessToast('删除成功');

    // 重新获取数据
    await fetchMemoData();

    // 关闭删除对话框
    closeDeleteDialog();
  } catch (deleteError) {
    console.error('❌ [memoSidebar] 删除备忘录项目失败:', deleteError);
    showFailToast('删除失败');
  } finally {
    isDeleting.value = false;
  }
};

// 删除事件记忆
const handleDeleteEvent = async (item: IMemoItem) => {
  // 从item.value中提取event_id（假设格式包含ID信息）
  // 这里需要根据实际的数据格式来提取event_id
  // 由于备忘录数据经过处理，我们需要从原始数据中查找对应的event_id

  if (!originalUserProfile.value?.events?.events) {
    throw new Error('无法找到原始事件数据');
  }

  // 尝试通过描述文本匹配找到对应的事件
  const matchingEvent = originalUserProfile.value.events.events.find(
    (event: { description_text?: string; event_id: string }) =>
      event.description_text && item.value.includes(event.description_text.substring(0, 20)),
  );

  if (!matchingEvent) {
    throw new Error('无法找到对应的事件记录');
  }

  let userId = userStore.userInfo?.login;
  if (!userId) {
    const userInfo = await getUserInfo();
    userId = userInfo.login;
  }

  await deletePersonEvent({
    user_id: userId,
    event_id: matchingEvent.event_id,
  });
};

// 删除个人信息属性
const handleDeleteAttribute = async (item: IMemoItem) => {
  if (!originalUserProfile.value?.person) {
    throw new Error('无法找到原始用户档案数据');
  }

  let userId = userStore.userInfo?.login;
  if (!userId) {
    const userInfo = await getUserInfo();
    userId = userInfo.login;
  }

  const { person } = originalUserProfile.value;

  // 复制当前的key_attributes
  const currentAttributes = { ...(person.key_attributes || {}) };

  // 删除对应的属性
  // 这里需要根据item.label找到对应的属性键
  const attributeKey = item.label;
  delete currentAttributes[attributeKey];

  // 调用updatePerson API更新用户信息
  await updatePerson(person.person_id || '', {
    user_id: userId,
    canonical_name: person.canonical_name || '',
    aliases: person.aliases || '',
    relationships: person.relationships || [],
    profile_summary: person.profile_summary || '',
    key_attributes: currentAttributes,
    is_user: person.is_user || false,
    avatar: person.avatar || '',
  });
};
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.memo-sidebar-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.memo-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 55%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 0 1px var(--border-light);
  z-index: 1001;
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
    background: transparent;
    border-bottom: 1px solid var(--border-light);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-xl);
      right: var(--spacing-xl);
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
    }

    .title {
      font-size: var(--font-size-2xl);
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.5px;
    }

    .intimacy-display {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-right: var(--spacing-md);

      .intimacy-label {
        font-size: var(--font-size-xl);
        color: var(--text-secondary);
        font-weight: 500;
      }

      .intimacy-value {
        font-size: var(--font-size-xl);
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary-color) 0%, #00ffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.2);
        }
      }
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-tertiary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--bg-hover);
        border-color: var(--border-light);
        color: var(--text-primary);
        transform: scale(1.05);
        box-shadow: var(--shadow-medium);
      }

      &:active {
        transform: scale(0.95);
      }

      img {
        width: 20px;
        height: 20px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    background-color: transparent;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .loading-text {
        font-size: 24px; // 增加4px
        color: var(--text-secondary);
        animation: pulse 1.5s ease-in-out infinite;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      gap: var(--spacing-md);

      .error-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }

      .retry-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary-color);
        color: white;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: 22px; // 增加4px
        transition: all 0.3s ease;

        &:hover {
          background: var(--primary-color-hover);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }
    }

    .memo-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);

      .memo-category {
        .category-title {
          font-size: 26px; // 增加4px
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: var(--spacing-md);
          padding-bottom: var(--spacing-sm);
          border-bottom: 2px solid var(--primary-color);
          letter-spacing: -0.3px;
        }

        .memo-items {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .memo-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 12px;
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            &:hover {
              background: linear-gradient(135deg, rgba(0, 188, 212, 0.15) 0%, rgba(0, 255, 255, 0.1) 100%);
              border-color: rgba(0, 255, 255, 0.4);
              transform: translateY(-2px);
              box-shadow:
                0 6px 20px rgba(0, 255, 255, 0.2),
                0 0 15px rgba(0, 255, 255, 0.3);

              .memo-label {
                color: rgba(255, 255, 255, 0.9);
              }

              .memo-value {
                color: #ffffff;
              }

              .memo-actions {
                opacity: 1;
              }
            }

            .memo-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 4px;
            }

            .memo-label {
              font-size: 22px; // 增加4px
              font-weight: 600;
              color: var(--text-secondary);
              margin-bottom: 4px;
              transition: color 0.3s ease;
            }

            .memo-value {
              font-size: 22px; // 增加4px
              font-weight: 550;
              color: var(--text-primary);
              line-height: 1.4;
              transition: color 0.3s ease;
            }

            .memo-actions {
              display: flex;
              align-items: center;
              gap: 8px;
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            .delete-memo-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 32px;
              height: 32px;
              background: rgba(255, 77, 77, 0.2);
              border: 1px solid rgba(255, 77, 77, 0.4);
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 77, 77, 0.3);
                border-color: rgba(255, 77, 77, 0.6);
                transform: scale(1.05);
              }

              img {
                width: 16px;
                height: 16px;
                filter: brightness(0) invert(1);
                opacity: 0.8;
              }

              &:hover img {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  transition: all 0.3s ease;
}

/* 添加一些微动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.memo-sidebar.sidebar-open {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}
</style>
